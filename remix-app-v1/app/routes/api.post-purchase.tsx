import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Authenticate the request from the post-purchase extension
    const { admin, session } = await authenticate.admin(request);
    
    if (request.method !== "POST") {
      return json({ error: "Method not allowed" }, { status: 405 });
    }

    const body = await request.json();
    const { orderId, variantId, quantity = 1 } = body;

    if (!orderId || !variantId) {
      return json({ error: "Missing required parameters" }, { status: 400 });
    }

    // In a real implementation, you would:
    // 1. Validate the order exists and belongs to the current session
    // 2. Check if the variant is available
    // 3. Calculate pricing including any discounts
    // 4. Create a new order or modify the existing one
    // 5. Handle payment processing for the additional amount

    // For this example, we'll simulate adding a product to an order
    const response = await admin.graphql(`
      mutation orderEditBegin($id: ID!) {
        orderEditBegin(id: $id) {
          calculatedOrder {
            id
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      variables: {
        id: orderId,
      },
    });

    const orderEditData = await response.json();

    if (orderEditData.data?.orderEditBegin?.userErrors?.length > 0) {
      return json({ 
        error: "Failed to begin order edit",
        details: orderEditData.data.orderEditBegin.userErrors
      }, { status: 400 });
    }

    // Add line item to the order
    const addLineResponse = await admin.graphql(`
      mutation orderEditAddVariant($id: ID!, $variantId: ID!, $quantity: Int!) {
        orderEditAddVariant(id: $id, variantId: $variantId, quantity: $quantity) {
          calculatedLineItem {
            id
            quantity
            variant {
              id
              title
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      variables: {
        id: orderEditData.data.orderEditBegin.calculatedOrder.id,
        variantId,
        quantity,
      },
    });

    const addLineData = await addLineResponse.json();

    if (addLineData.data?.orderEditAddVariant?.userErrors?.length > 0) {
      return json({ 
        error: "Failed to add variant to order",
        details: addLineData.data.orderEditAddVariant.userErrors
      }, { status: 400 });
    }

    // Commit the order edit
    const commitResponse = await admin.graphql(`
      mutation orderEditCommit($id: ID!) {
        orderEditCommit(id: $id, notifyCustomer: true) {
          order {
            id
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      variables: {
        id: orderEditData.data.orderEditBegin.calculatedOrder.id,
      },
    });

    const commitData = await commitResponse.json();

    if (commitData.data?.orderEditCommit?.userErrors?.length > 0) {
      return json({ 
        error: "Failed to commit order edit",
        details: commitData.data.orderEditCommit.userErrors
      }, { status: 400 });
    }

    return json({ 
      success: true,
      order: commitData.data.orderEditCommit.order
    });

  } catch (error) {
    console.error("Post-purchase API error:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};
