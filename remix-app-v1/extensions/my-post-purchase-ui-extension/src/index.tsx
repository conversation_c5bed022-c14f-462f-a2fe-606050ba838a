/**
 * Extend Shopify Checkout with a custom Post Purchase user experience.
 * This extension provides an upsell offer with product selection and size options.
 */
import React, { useState } from 'react';

import {
  extend,
  render,
  BlockStack,
  Button,
  CalloutBanner,
  Heading,
  Image,
  Layout,
  TextContainer,
  View,
  Select,
  Text,
  InlineStack,
  Separator,
} from "@shopify/post-purchase-ui-extensions-react";

// Types
interface Color {
  name: string;
  value: string;
}

interface Variant {
  id: string;
  title: string;
  price: string;
  available: boolean;
}

interface UpsellProduct {
  id: string;
  title: string;
  description: string;
  image: string;
  variants: Variant[];
  colors: Color[];
}

/**
 * Entry point for the `ShouldRender` Extension Point.
 */
extend("Checkout::PostPurchase::ShouldRender", async ({ storage, inputData }: any) => {
  try {
    // Get product data for upsell offer
    const upsellProduct = await getUpsellProduct();

    // Only show if we have a valid upsell product
    const render = !!upsellProduct;

    if (render) {
      await storage.update({
        upsellProduct,
        originalOrder: inputData?.initialPurchase,
      });
    }

    return { render };
  } catch (error) {
    console.error('ShouldRender error:', error);
    return { render: false };
  }
});

// Get upsell product data
async function getUpsellProduct() {
  // In a real implementation, this would fetch from your app's API
  // For now, we'll return mock data that matches the design
  return {
    id: "gid://shopify/Product/123456789",
    title: "Premium Running Shoes",
    description: "Get an additional pair for your daily use or sports for just $89.99 extra 52% off! This offer is only available right here, right now.",
    image: "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop&crop=center",
    variants: [
      { id: "variant-1", title: "Size 7", price: "89.99", available: true },
      { id: "variant-2", title: "Size 8", price: "89.99", available: true },
      { id: "variant-3", title: "Size 9", price: "89.99", available: true },
      { id: "variant-4", title: "Size 10", price: "89.99", available: true },
      { id: "variant-5", title: "Size 11", price: "89.99", available: true },
    ],
    colors: [
      { name: "Green", value: "#4CAF50" },
      { name: "Yellow", value: "#FFEB3B" },
      { name: "Blue", value: "#2196F3" },
      { name: "Gray", value: "#9E9E9E" },
      { name: "Black", value: "#000000" },
    ]
  };
}

/**
 * Entry point for the `Render` Extension Point
 */
render("Checkout::PostPurchase::Render", App);

// Timer component - simplified
function CountdownTimer() {
  return (
    <Text size="large" emphasis="strong">
      13:09:00
    </Text>
  );
}

// Color selector component
interface ColorSelectorProps {
  colors: Color[];
  selectedColor: number;
  onColorChange: (index: number) => void;
}

function ColorSelector({ colors, selectedColor, onColorChange }: ColorSelectorProps) {
  return (
    <InlineStack spacing="tight">
      {colors.map((color, index) => (
        <Button
          key={index}
          kind={selectedColor === index ? "primary" : "secondary"}
          size="small"
          onPress={() => onColorChange(index)}
        >
          {color.name}
        </Button>
      ))}
    </InlineStack>
  );
}

// Main App component
interface AppProps {
  storage: any;
  inputData?: any;
  done: () => void;
}

export function App({ storage, inputData, done }: AppProps) {
  const [selectedSize, setSelectedSize] = useState("");
  const [selectedColor, setSelectedColor] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const { upsellProduct } = storage?.initialData || {};

  if (!upsellProduct) {
    return (
      <BlockStack spacing="loose">
        <TextContainer>
          <Heading>Loading...</Heading>
        </TextContainer>
      </BlockStack>
    );
  }

  const handleUpgrade = async () => {
    if (!selectedSize) {
      // Show error - size must be selected
      return;
    }

    setIsLoading(true);

    try {
      // Find selected variant
      const selectedVariant = upsellProduct.variants.find((v: Variant) => v.id === selectedSize);

      if (selectedVariant) {
        // Call our app's API to add the product to the order
        const response = await fetch('/api/post-purchase', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderId: inputData?.initialPurchase?.referenceId || 'test-order',
            variantId: selectedVariant.id,
            quantity: 1,
          }),
        });

        const result = await response.json();

        if (result.success) {
          // Success - complete the extension
          done();
        } else {
          console.error('Failed to add product:', result.error);
          // In a real app, you'd show an error message to the user
        }
      }
    } catch (error) {
      console.error('Error adding product to order:', error);
      // In a real app, you'd show an error message to the user
    } finally {
      setIsLoading(false);
    }
  };

  const sizeOptions = upsellProduct.variants.map((variant: Variant) => ({
    value: variant.id,
    label: variant.title,
  }));

  return (
    <BlockStack spacing="loose">
      {/* Header */}
      <TextContainer>
        <Heading level={1}>Wait! Before we complete your order</Heading>
      </TextContainer>

      {/* Profile section */}
      <BlockStack spacing="tight">
        <Text size="small" emphasis="strong">Sarah - Upsteps Care Team</Text>
        <Text size="small">I've got one last REALLY special offer for you.</Text>
      </BlockStack>

      <Separator />

      {/* Timer */}
      <TextContainer alignment="center">
        <CountdownTimer />
      </TextContainer>

      {/* Product section */}
      <Layout
        maxInlineSize={1}
        media={[
          { viewportSize: "small", sizes: [1] },
          { viewportSize: "medium", sizes: [0.4, 0.6] },
          { viewportSize: "large", sizes: [0.4, 0.6] },
        ]}
      >
        <View>
          <Image
            source={upsellProduct.image}
          />
        </View>

        <BlockStack spacing="base">
          {/* Product title and description */}
          <BlockStack spacing="tight">
            <Heading level={2}>{upsellProduct.title}</Heading>
            <Text size="small">
              Get an additional pair for your daily use or sports for just $89.99 extra 52% off!
              This offer is only available right here, right now.
            </Text>
          </BlockStack>

          {/* Size selector */}
          <BlockStack spacing="tight">
            <Text size="small" emphasis="strong">Choose size</Text>
            <Select
              label="Size"
              value={selectedSize}
              onChange={setSelectedSize}
              options={sizeOptions}
              placeholder="Select size"
            />
          </BlockStack>

          {/* Color selector */}
          <BlockStack spacing="tight">
            <Text size="small" emphasis="strong">Choose color</Text>
            <ColorSelector
              colors={upsellProduct.colors}
              selectedColor={selectedColor}
              onColorChange={setSelectedColor}
            />
          </BlockStack>

          {/* Action buttons */}
          <BlockStack spacing="base">
            <Button
              submit
              loading={isLoading}
              onPress={handleUpgrade}
              disabled={!selectedSize}
            >
              YES! UPGRADE MY ORDER NOW!
            </Button>

            <Button
              kind="plain"
              onPress={() => done()}
            >
              No, thanks
            </Button>
          </BlockStack>
        </BlockStack>
      </Layout>

      {/* Disclaimer */}
      <CalloutBanner>
        <Text size="small">
          It costs less if we make additional pairs for you at the same time. Instead
          of packing the difference, we want you to enjoy it. Once you complete
          your purchase, this offer will be gone.
        </Text>
      </CalloutBanner>
    </BlockStack>
  );
}